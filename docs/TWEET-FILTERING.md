# Tweet Filtering System

## Overview

We've implemented a comprehensive tweet filtering system that allows you to fetch and filter tweets related to your monitored accounts.

## What You Can Do

### 1. Get User's Own Tweets
Fetch tweets posted by any Twitter user (including your monitored accounts):

```typescript
// Fetch tweets from a specific user
const userTweets = await trpc.twitter.fetchUserLastTweets.query({
  username: "elonmus<PERSON>",
  includeReplies: false,    // Filter out replies
  includeRetweets: true,    // Include retweets
  limit: 20
});
```

### 2. Get All Tweets for a Monitored Account
Use the new unified filtering endpoint to get any combination of:
- **Mentions**: Tweets that mention the account
- **User Tweets**: Tweets posted by the account
- **Replies**: Reply tweets (can be filtered)
- **Retweets**: Retweets (can be filtered)

```typescript
// Get all tweet types for a monitored account
const allTweets = await trpc.mentions.getAccountTweets.query({
  accountId: "your-account-id",
  tweetTypes: {
    mentions: true,      // Get mentions of this account
    userTweets: true,    // Get tweets by this account
    replies: true,       // Include replies (for userTweets)
    retweets: true       // Include retweets (for userTweets)
  },
  sortBy: 'createdAt',   // Or 'bullishScore', 'importanceScore'
  sortOrder: 'desc'
});

// Get only mentions
const onlyMentions = await trpc.mentions.getAccountTweets.query({
  accountId: "your-account-id",
  tweetTypes: {
    mentions: true,
    userTweets: false,
    replies: false,
    retweets: false
  }
});

// Get only original tweets (no replies, no retweets)
const originalTweets = await trpc.mentions.getAccountTweets.query({
  accountId: "your-account-id",
  tweetTypes: {
    mentions: false,
    userTweets: true,
    replies: false,      // Exclude replies
    retweets: false      // Exclude retweets
  }
});
```

## Database Storage

All tweets are stored in the `mentions` table with the `isUserTweet` flag:
- `isUserTweet: false` - This is a mention of the account
- `isUserTweet: true` - This is a tweet by the account

## React Component Usage

Use the `AccountTweetsFilter` component for a full UI with filters:

```tsx
import { AccountTweetsFilter } from '@/components/account-tweets-filter';

<AccountTweetsFilter accountId="your-monitored-account-id" />
```

## Features

- **Flexible Filtering**: Mix and match tweet types
- **Sorting Options**: Sort by date, bullish score, or importance
- **Pagination**: Built-in cursor-based pagination
- **Real-time Updates**: Fetch fresh tweets from Twitter API
- **Performance**: Optimized database queries with proper indexes
- **Type Safety**: Full TypeScript support

## API Endpoints

### 1. `twitter.fetchUserLastTweets`
Fetches tweets from any Twitter user and stores them as `isUserTweet: true`

### 2. `mentions.getAccountTweets` 
Unified endpoint to get all tweet types with advanced filtering

### 3. Existing endpoints still work:
- `mentions.getAll` - Get mentions with basic filters
- `twitter.fetchUserMentions` - Fetch mentions from Twitter API