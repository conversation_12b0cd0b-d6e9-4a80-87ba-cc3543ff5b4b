### General Stack

- Frontend Routing: NextJS
- Database: PostgreSQL
- Database Service: Supabase
- Memory Provider: Mem0 (https://docs.mem0.ai/components/vectordbs/dbs/supabase)
- ORM: Prisma
- Authentication: Clerk
- Package Manager & JavaScript Runtime: pnpm
- Full-stack Framework (Frontend & API Routes): Next.js
- Monorepo Tool: Turborepo
- Agent Framework: AISDK from Vercel
- Openrouter: Model Provider
- Prefered Models: Gemini 2.5 Flash for simpel tasks, Gemini 2.5 Pro for difficult tasks
- Debugging: Sentry & Sentry MCP
- Twitter Data Provider: https://docs.twitterapi.io/introduction
- Image Storage: https://docs.uploadthing.com/

### Benji Agent
It's an AI Agent, made with the AISDK framework by Vercel.
It uses the Openrouter provider and it has 2 levels of intelligence, consider them upgrades.
The basic one is Gemini 2.5 Flash, the pro one is OpenAI o3, so we are going to use the Openouter provider with both OPENAI & GEMINI.
It has access to three tools:
1. xAI Live Search: https://docs.x.ai/docs/guides/live-search#parameter-safe_search-supported-by-web-and-news
2. Exa Search: https://docs.exa.ai/reference/getting-started
3. Image Generation with Gen1 by Openai: https://platform.openai.com/docs/guides/tools-image-generation#supported-models
4. Mem0 User Memory
It has a Max Step of 5