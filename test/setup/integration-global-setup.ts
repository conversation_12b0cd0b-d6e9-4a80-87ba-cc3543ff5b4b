/**
 * Integration Global Setup
 * 
 * Global setup for integration tests - runs once before all integration tests
 */

import { execSync } from 'child_process'
import { PrismaClient } from '../../../prisma/generated'

export default async function integrationGlobalSetup() {
  console.log('🚀 Integration Global Setup: Starting...')
  
  try {
    // 1. Verify test environment
    console.log('🔍 Verifying test environment...')
    await verifyTestEnvironment()
    
    // 2. Setup test database
    console.log('📊 Setting up integration test database...')
    await setupIntegrationDatabase()
    
    // 3. Run database migrations
    console.log('🔄 Running database migrations for integration tests...')
    await runIntegrationMigrations()
    
    // 4. Seed integration test data
    console.log('🌱 Seeding integration test data...')
    await seedIntegrationData()
    
    // 5. Verify external service mocks
    console.log('🎭 Verifying external service mocks...')
    await verifyServiceMocks()
    
    console.log('✅ Integration Global Setup: Complete')
  } catch (error) {
    console.error('❌ Integration Global Setup: Failed', error)
    throw error
  }
}

/**
 * Verify test environment configuration
 */
async function verifyTestEnvironment() {
  // Check NODE_ENV
  if (process.env.NODE_ENV !== 'test') {
    throw new Error('Integration tests must run with NODE_ENV=test')
  }
  
  // Check test database URL
  const testDbUrl = process.env.DATABASE_URL
  if (!testDbUrl || !testDbUrl.includes('_test')) {
    throw new Error('Integration tests require a test database (URL must contain "_test")')
  }
  
  // Check required environment variables
  const requiredVars = [
    'DATABASE_URL',
    'DIRECT_URL',
    'CLERK_SECRET_KEY',
    'OPENROUTER_API_KEY',
    'TWITTER_API_KEY'
  ]
  
  const missingVars = requiredVars.filter(key => !process.env[key])
  if (missingVars.length > 0) {
    console.warn('⚠️ Missing environment variables (using mocks):', missingVars)
  }
  
  console.log('✅ Test environment verified')
}

/**
 * Setup integration test database
 */
async function setupIntegrationDatabase() {
  const testDbUrl = process.env.DATABASE_URL!
  
  console.log('📊 Integration test database URL:', testDbUrl.replace(/password=[^&\s]+/, 'password=***'))
  
  // Test database connection
  const prisma = new PrismaClient({
    datasources: {
      db: {
        url: testDbUrl
      }
    }
  })
  
  try {
    await prisma.$connect()
    console.log('✅ Integration test database connection successful')
    
    // Test basic query
    await prisma.$queryRaw`SELECT 1 as test`
    console.log('✅ Integration test database query successful')
  } catch (error) {
    console.error('❌ Integration test database connection failed:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

/**
 * Run database migrations for integration tests
 */
async function runIntegrationMigrations() {
  try {
    // Reset database to clean state
    console.log('🔄 Resetting integration test database...')
    execSync('pnpm db:migrate reset --force --skip-seed', {
      stdio: 'inherit',
      env: {
        ...process.env,
        DATABASE_URL: process.env.DATABASE_URL
      }
    })
    
    console.log('✅ Integration test database migrations complete')
  } catch (error) {
    console.error('❌ Integration test database migrations failed:', error)
    throw error
  }
}

/**
 * Seed integration test data
 */
async function seedIntegrationData() {
  const prisma = new PrismaClient()
  
  try {
    // Create comprehensive test subscription plans
    await prisma.subscriptionPlan.createMany({
      data: [
        {
          id: 'integration-reply-guy',
          name: 'reply-guy',
          displayName: 'Reply Guy',
          description: 'Integration test plan for Reply Guy',
          price: 20.00,
          isActive: true
        },
        {
          id: 'integration-reply-god',
          name: 'reply-god',
          displayName: 'Reply God',
          description: 'Integration test plan for Reply God',
          price: 50.00,
          isActive: true
        },
        {
          id: 'integration-team',
          name: 'team',
          displayName: 'Team',
          description: 'Integration test plan for Team',
          price: 79.00,
          isActive: true
        }
      ],
      skipDuplicates: true
    })
    
    // Create comprehensive test plan features
    await prisma.planFeature.createMany({
      data: [
        // Reply Guy features
        { planId: 'integration-reply-guy', feature: 'AI_CALLS', limit: 100 },
        { planId: 'integration-reply-guy', feature: 'IMAGE_GENERATIONS', limit: 20 },
        { planId: 'integration-reply-guy', feature: 'MONITORED_ACCOUNTS', limit: 3 },
        { planId: 'integration-reply-guy', feature: 'MENTIONS_PER_SYNC', limit: 50 },
        { planId: 'integration-reply-guy', feature: 'MAX_TOTAL_MENTIONS', limit: 1000 },
        
        // Reply God features
        { planId: 'integration-reply-god', feature: 'AI_CALLS', limit: 500 },
        { planId: 'integration-reply-god', feature: 'IMAGE_GENERATIONS', limit: 50 },
        { planId: 'integration-reply-god', feature: 'MONITORED_ACCOUNTS', limit: 10 },
        { planId: 'integration-reply-god', feature: 'MENTIONS_PER_SYNC', limit: 100 },
        { planId: 'integration-reply-god', feature: 'MAX_TOTAL_MENTIONS', limit: 5000 },
        
        // Team features
        { planId: 'integration-team', feature: 'AI_CALLS', limit: 1000 },
        { planId: 'integration-team', feature: 'IMAGE_GENERATIONS', limit: 100 },
        { planId: 'integration-team', feature: 'MONITORED_ACCOUNTS', limit: 25 },
        { planId: 'integration-team', feature: 'MENTIONS_PER_SYNC', limit: 200 },
        { planId: 'integration-team', feature: 'MAX_TOTAL_MENTIONS', limit: 10000 }
      ],
      skipDuplicates: true
    })
    
    // Create comprehensive test AI models
    await prisma.aIModel.createMany({
      data: [
        {
          id: 'integration-gemini-flash',
          name: 'gemini25Flash',
          displayName: 'Gemini 2.5 Flash',
          description: 'Fast Gemini model for integration tests',
          provider: 'google',
          modelId: 'google/gemini-2.5-flash',
          costTier: 'low',
          speed: 'fast',
          isActive: true
        },
        {
          id: 'integration-gpt-4o',
          name: 'gpt4o',
          displayName: 'GPT-4o',
          description: 'Advanced GPT-4o model for integration tests',
          provider: 'openai',
          modelId: 'gpt-4o',
          costTier: 'high',
          speed: 'medium',
          isActive: true
        },
        {
          id: 'integration-claude-sonnet',
          name: 'claude35Sonnet',
          displayName: 'Claude 3.5 Sonnet',
          description: 'Claude 3.5 Sonnet for integration tests',
          provider: 'anthropic',
          modelId: 'claude-3-5-sonnet',
          costTier: 'medium',
          speed: 'medium',
          isActive: true
        },
        {
          id: 'integration-grok-beta',
          name: 'grokBeta',
          displayName: 'Grok Beta',
          description: 'Grok Beta for integration tests',
          provider: 'xai',
          modelId: 'grok-beta',
          costTier: 'medium',
          speed: 'fast',
          isActive: true
        }
      ],
      skipDuplicates: true
    })
    
    // Create comprehensive test personality profiles
    await prisma.personalityProfile.createMany({
      data: [
        {
          id: 'integration-professional',
          name: 'Professional',
          description: 'Professional and courteous responses',
          systemPrompt: 'You are a professional AI assistant that provides helpful, accurate, and courteous responses.',
          isActive: true,
          isDefault: true
        },
        {
          id: 'integration-casual',
          name: 'Casual',
          description: 'Casual and friendly responses',
          systemPrompt: 'You are a casual and friendly AI assistant that uses a conversational tone.',
          isActive: true,
          isDefault: false
        },
        {
          id: 'integration-technical',
          name: 'Technical',
          description: 'Technical and detailed responses',
          systemPrompt: 'You are a technical AI assistant that provides detailed, accurate technical information.',
          isActive: true,
          isDefault: false
        },
        {
          id: 'integration-creative',
          name: 'Creative',
          description: 'Creative and engaging responses',
          systemPrompt: 'You are a creative AI assistant that provides engaging and imaginative responses.',
          isActive: true,
          isDefault: false
        }
      ],
      skipDuplicates: true
    })
    
    console.log('✅ Integration test data seeded successfully')
  } catch (error) {
    console.error('❌ Integration test data seeding failed:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

/**
 * Verify external service mocks
 */
async function verifyServiceMocks() {
  // Verify mock environment variables are set
  const mockVars = [
    'OPENROUTER_API_KEY',
    'TWITTER_API_KEY',
    'OPENAI_API_KEY',
    'CLERK_SECRET_KEY',
    'XAI_API_KEY',
    'PERPLEXITY_API_KEY',
    'EXA_API_KEY'
  ]
  
  const missingMockVars = mockVars.filter(key => !process.env[key])
  
  if (missingMockVars.length > 0) {
    console.warn('⚠️ Missing mock environment variables:', missingMockVars)
    console.warn('⚠️ Some integration tests may fail without proper mocks')
  }
  
  // Set default mock values for missing variables
  missingMockVars.forEach(key => {
    process.env[key] = `mock_${key.toLowerCase()}`
  })
  
  console.log('✅ External service mocks verified')
}

/**
 * Global teardown for integration tests
 */
export async function integrationGlobalTeardown() {
  console.log('🧹 Integration Global Teardown: Starting...')
  
  try {
    // Clean up integration test database
    const prisma = new PrismaClient()
    
    // Delete all integration test data
    await prisma.$transaction([
      prisma.usageLog.deleteMany({
        where: {
          userId: {
            startsWith: 'integration-'
          }
        }
      }),
      prisma.aIResponse.deleteMany({
        where: {
          userId: {
            startsWith: 'integration-'
          }
        }
      }),
      prisma.mention.deleteMany({
        where: {
          userId: {
            startsWith: 'integration-'
          }
        }
      }),
      prisma.monitoredAccount.deleteMany({
        where: {
          userId: {
            startsWith: 'integration-'
          }
        }
      }),
      prisma.image.deleteMany({
        where: {
          userId: {
            startsWith: 'integration-'
          }
        }
      }),
      prisma.user.deleteMany({
        where: {
          id: {
            startsWith: 'integration-'
          }
        }
      }),
      prisma.planFeature.deleteMany({
        where: {
          planId: {
            startsWith: 'integration-'
          }
        }
      }),
      prisma.subscriptionPlan.deleteMany({
        where: {
          id: {
            startsWith: 'integration-'
          }
        }
      }),
      prisma.personalityProfile.deleteMany({
        where: {
          id: {
            startsWith: 'integration-'
          }
        }
      }),
      prisma.aIModel.deleteMany({
        where: {
          id: {
            startsWith: 'integration-'
          }
        }
      })
    ])
    
    await prisma.$disconnect()
    
    console.log('✅ Integration Global Teardown: Complete')
  } catch (error) {
    console.error('❌ Integration Global Teardown: Failed', error)
  }
}
