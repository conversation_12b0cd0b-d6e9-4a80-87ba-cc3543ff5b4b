/**
 * Benji AI Agent Unit Tests
 * 
 * Tests for the core AI agent functionality
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { BenjiAgent, getBenjiForUser } from '../../../apps/web/src/lib/benji-agent'
import { mockAIProviders, mockPrismaClient, mockEnvironmentVariables, clearAllMocks } from '../../utils/mocks'

// Mock dependencies
vi.mock('ai')
vi.mock('../../../apps/web/src/lib/db-utils')
vi.mock('../../../apps/web/src/lib/ai-providers')
vi.mock('../../../apps/web/src/lib/tools/xai-search')
vi.mock('../../../apps/web/src/lib/tools/exa-search')
vi.mock('../../../apps/web/src/lib/tools/openai-image')

describe('Benji AI Agent', () => {
  let mockPrisma: any
  
  beforeEach(() => {
    clearAllMocks()
    mockPrisma = mockPrismaClient()
    mockAIProviders()
    mockEnvironmentVariables()
    
    // Mock AI providers module
    vi.mocked(require('../../../apps/web/src/lib/ai-providers').getModel).mockReturnValue({
      provider: 'mock',
      model: 'mock-model'
    })

    vi.mocked(require('../../../apps/web/src/lib/ai-providers').getModelByPlan).mockReturnValue('gemini25Flash')
    
    console.log('🧪 Benji Agent Test: Setup complete')
  })
  
  describe('BenjiAgent Constructor', () => {
    it('should create agent with default configuration', () => {
      // Act
      const agent = new BenjiAgent()
      
      // Assert
      expect(agent).toBeInstanceOf(BenjiAgent)
      
      console.log('✅ Benji Agent Test: Default constructor works')
    })
    
    it('should create agent with custom configuration', () => {
      // Arrange
      const config = {
        model: 'gpt4o' as any,
        userId: 'test-user-123',
        userPlan: 'reply-god',
        maxTokens: 8000,
        temperature: 0.5,
        enableTools: false,
        maxSteps: 3,
        personalityPrompt: 'Be professional',
        customSystemPrompt: 'Custom prompt'
      }
      
      // Act
      const agent = new BenjiAgent(config)
      
      // Assert
      expect(agent).toBeInstanceOf(BenjiAgent)
      
      console.log('✅ Benji Agent Test: Custom constructor works')
    })
    
    it('should auto-select model based on user plan', () => {
      // Arrange
      const config = {
        userPlan: 'reply-god'
      }
      
      // Act
      const agent = new BenjiAgent(config)
      
      // Assert
      expect(vi.mocked(require('../../../apps/web/src/lib/ai-providers').getModelByPlan)).toHaveBeenCalledWith('reply-god')
      
      console.log('✅ Benji Agent Test: Plan-based model selection works')
    })
  })
  
  describe('generateMentionResponse', () => {
    it('should generate AI response for mention', async () => {
      // Arrange
      const agent = new BenjiAgent({
        userId: 'test-user-123',
        enableTools: false
      })
      
      const mentionContent = 'This is a test mention about crypto'
      const context = {
        authorInfo: {
          name: 'Test User',
          handle: 'testuser',
          avatarUrl: 'https://example.com/avatar.jpg'
        }
      }
      
      // Mock streamText response
      const mockStream = {
        textStream: async function* () {
          yield 'This is a mock AI response'
          yield ' for testing purposes.'
        },
        text: 'This is a mock AI response for testing purposes.',
        finishReason: 'stop',
        usage: {
          promptTokens: 50,
          completionTokens: 20,
          totalTokens: 70
        }
      }
      
      vi.mocked(require('ai').streamText).mockResolvedValue(mockStream)

      // Act
      const result = await agent.generateMentionResponse(mentionContent, context)

      // Assert
      expect(result).toBeDefined()
      expect(vi.mocked(require('ai').streamText)).toHaveBeenCalled()

      const streamTextCall = vi.mocked(require('ai').streamText).mock.calls[0][0]
      expect(streamTextCall.messages).toBeInstanceOf(Array)
      expect(streamTextCall.messages.length).toBeGreaterThan(0)
      expect(streamTextCall.maxTokens).toBe(4000) // Default value
      expect(streamTextCall.temperature).toBe(0.7) // Default value
      
      console.log('✅ Benji Agent Test: Response generation works')
    })
    
    it('should include tools when enabled', async () => {
      // Arrange
      const agent = new BenjiAgent({
        userId: 'test-user-123',
        enableTools: true
      })
      
      const mockStream = {
        textStream: async function* () {
          yield 'Response with tools'
        },
        text: 'Response with tools',
        finishReason: 'stop'
      }
      
      vi.mocked(require('ai').streamText).mockResolvedValue(mockStream)
      
      // Act
      await agent.generateMentionResponse('Test mention', {
        authorInfo: {
          name: 'Test',
          handle: 'test'
        }
      })
      
      // Assert
      const streamTextCall = vi.mocked(require('ai').streamText).mock.calls[0][0]
      expect(streamTextCall.tools).toBeDefined()
      expect(streamTextCall.tools).toHaveProperty('searchWeb')
      expect(streamTextCall.tools).toHaveProperty('searchKnowledge')
      expect(streamTextCall.tools).toHaveProperty('generateImage')
      
      console.log('✅ Benji Agent Test: Tools integration works')
    })
    
    it('should handle AI provider errors gracefully', async () => {
      // Arrange
      const agent = new BenjiAgent({
        userId: 'test-user-123'
      })
      
      // Mock AI provider error
      vi.mocked(require('ai').streamText).mockRejectedValue(new Error('AI provider error'))
      
      // Act & Assert
      await expect(agent.generateMentionResponse('Test mention', {
        authorInfo: {
          name: 'Test',
          handle: 'test'
        }
      })).rejects.toThrow('AI provider error')
      
      console.log('✅ Benji Agent Test: Error handling works')
    })
  })
  
  describe('performFullAnalysis', () => {
    it('should analyze tweet content and return structured data', async () => {
      // Arrange
      const agent = new BenjiAgent({
        userId: 'test-user-123'
      })
      
      const tweetContent = 'Bitcoin is going to the moon! 🚀 #crypto #bullish'
      const authorInfo = {
        name: 'Crypto Enthusiast',
        handle: 'cryptofan',
        followers: 5000,
        verified: true,
        avatarUrl: 'https://example.com/crypto-avatar.jpg'
      }
      
      // Mock AI analysis response
      const mockAnalysisResponse = {
        text: JSON.stringify({
          bullishScore: 85,
          importanceScore: 70,
          keywords: ['bitcoin', 'crypto', 'bullish', 'moon'],
          sentiment: 'positive',
          priority: 'high',
          reasoning: 'Strong bullish sentiment with popular crypto keywords'
        }),
        finishReason: 'stop'
      }
      
      vi.mocked(require('ai').generateText).mockResolvedValue(mockAnalysisResponse)
      
      // Act
      const result = await agent.performFullAnalysis(tweetContent, authorInfo)
      
      // Assert
      expect(result).toMatchObject({
        bullishScore: 85,
        importanceScore: 70,
        keywords: expect.arrayContaining(['bitcoin', 'crypto', 'bullish', 'moon']),
        analysisData: expect.objectContaining({
          sentiment: 'positive',
          priority: 'high'
        })
      })
      
      console.log('✅ Benji Agent Test: Full analysis works')
    })
    
    it('should handle malformed analysis response', async () => {
      // Arrange
      const agent = new BenjiAgent({
        userId: 'test-user-123'
      })
      
      // Mock malformed response
      const mockMalformedResponse = {
        text: 'This is not valid JSON',
        finishReason: 'stop'
      }
      
      vi.mocked(require('ai').generateText).mockResolvedValue(mockMalformedResponse)
      
      // Act
      const result = await agent.performFullAnalysis('Test content', {
        name: 'Test',
        handle: 'test'
      })
      
      // Assert
      expect(result).toMatchObject({
        bullishScore: 50, // Default fallback
        importanceScore: 50, // Default fallback
        keywords: [],
        analysisData: expect.any(Object)
      })
      
      console.log('✅ Benji Agent Test: Malformed response handling works')
    })
  })
  
  describe('getBenjiForUser', () => {
    it('should create agent for user with database settings', async () => {
      // Arrange
      const userId = 'test-user-123'
      const mockUser = {
        id: userId,
        plan: {
          name: 'reply-god'
        },
        selectedPersonality: {
          systemPrompt: 'Be professional and helpful'
        },
        selectedModel: {
          name: 'gpt4o'
        },
        customSystemPrompt: 'Custom user prompt'
      }
      
      mockPrisma.user.findUnique.mockResolvedValue(mockUser)
      
      // Act
      const agent = await getBenjiForUser(userId)
      
      // Assert
      expect(agent).toBeInstanceOf(BenjiAgent)
      expect(mockPrisma.user.findUnique).toHaveBeenCalledWith({
        where: { id: userId },
        include: {
          plan: true,
          selectedPersonality: true,
          selectedModel: true
        }
      })
      
      console.log('✅ Benji Agent Test: User-specific agent creation works')
    })
    
    it('should handle missing environment variables', async () => {
      // Arrange
      const userId = 'test-user-123'
      delete process.env.OPENROUTER_API_KEY
      
      // Act & Assert
      await expect(getBenjiForUser(userId)).rejects.toThrow(
        'Missing required environment variables: OPENROUTER_API_KEY'
      )
      
      console.log('✅ Benji Agent Test: Environment validation works')
    })
    
    it('should create agent with defaults for user without settings', async () => {
      // Arrange
      const userId = 'test-user-minimal'
      const mockUser = {
        id: userId,
        plan: {
          name: 'reply-guy'
        },
        selectedPersonality: null,
        selectedModel: null,
        customSystemPrompt: null
      }
      
      mockPrisma.user.findUnique.mockResolvedValue(mockUser)
      
      // Act
      const agent = await getBenjiForUser(userId)
      
      // Assert
      expect(agent).toBeInstanceOf(BenjiAgent)
      
      console.log('✅ Benji Agent Test: Default settings fallback works')
    })
    
    it('should handle non-existent user', async () => {
      // Arrange
      const userId = 'non-existent-user'
      mockPrisma.user.findUnique.mockResolvedValue(null)
      
      // Act & Assert
      await expect(getBenjiForUser(userId)).rejects.toThrow('User not found')
      
      console.log('✅ Benji Agent Test: Non-existent user handling works')
    })
  })
})
