# Database Schema Sync Instructions

## Current Issue
The `useFirst<PERSON>erson` field exists in the Prisma schema but not in the actual database, causing tRPC errors when trying to update personality settings.

## Required Database Changes
The following field needs to be added to the `users` table:

```sql
ALTER TABLE users ADD COLUMN "useFirst<PERSON>erson" BOOLEAN NOT NULL DEFAULT true;
```

## Commands to Run (in order)

### Option 1: Using Prisma Push
```bash
cd apps/web
pnpm db:push
```

### Option 2: Manual SQL (if push fails)
```sql
-- Connect to your Supabase database and run:
ALTER TABLE users ADD COLUMN "useFirst<PERSON>erson" BOOLEAN NOT NULL DEFAULT true;
```

### Option 3: Prisma Migrate (recommended for production)
```bash
cd apps/web
npx prisma migrate dev --name add-use-first-person --schema ./prisma/schema/schema.prisma
```

## Verification
After running the database update, verify with:
```sql
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_name = 'users' AND column_name = 'useFirstPerson';
```

Should return:
```
column_name     | data_type | is_nullable | column_default
useFirstPerson  | boolean   | NO          | true
```

## What This Enables
- Users can toggle between 1st person (account owner) and 3rd person (external user) AI responses
- Personality selector UI will work without errors
- AI agent will generate appropriate prompts based on the setting

## Files Already Updated
- ✅ `apps/web/prisma/schema/schema.prisma` - Schema definition
- ✅ `apps/web/src/routers/user.ts` - Backend API
- ✅ `apps/web/src/components/ui/personality-selector.tsx` - UI component  
- ✅ `apps/web/src/lib/benji-agent.ts` - AI agent logic
- ✅ Prisma client regenerated locally

## Current Status
- Code: ✅ Complete and ready
- Database: ⏳ Pending sync
- Testing: ⏳ Pending database sync

Once you successfully push the schema, the 1st/3rd person feature will be fully functional.