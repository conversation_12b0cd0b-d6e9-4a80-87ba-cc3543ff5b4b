-- =====================================================
-- AI Models Migration for BuddyChip
-- Run this in Supabase SQL Editor
-- =====================================================

-- 1. Create AI Models table
CREATE TABLE IF NOT EXISTS "ai_models" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "displayName" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "provider" TEXT NOT NULL,
    "modelId" TEXT NOT NULL,
    "costTier" TEXT NOT NULL,
    "speed" TEXT NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ai_models_pkey" PRIMARY KEY ("id")
);

-- 2. Create unique constraint on ai_models.name
CREATE UNIQUE INDEX "ai_models_name_key" ON "ai_models"("name");

-- 3. Create indexes for ai_models
CREATE INDEX "ai_models_name_idx" ON "ai_models"("name");
CREATE INDEX "ai_models_isActive_idx" ON "ai_models"("isActive");
CREATE INDEX "ai_models_provider_idx" ON "ai_models"("provider");
CREATE INDEX "ai_models_costTier_idx" ON "ai_models"("costTier");

-- 4. Add modelId column to users table
ALTER TABLE "users" ADD COLUMN IF NOT EXISTS "modelId" TEXT;

-- 5. Create foreign key constraint
ALTER TABLE "users" ADD CONSTRAINT "users_modelId_fkey" 
    FOREIGN KEY ("modelId") REFERENCES "ai_models"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- 6. Create index for users.modelId
CREATE INDEX IF NOT EXISTS "users_modelId_idx" ON "users"("modelId");

-- 7. Insert the three AI models
INSERT INTO "ai_models" ("id", "name", "displayName", "description", "provider", "modelId", "costTier", "speed", "isActive") VALUES
    ('clz8k3m1x0000abc123def456', 'Workhorse', 'Workhorse', 'Fast and efficient model for everyday tasks. Great balance of speed and quality.', 'openrouter', 'google/gemini-2.5-flash', 'low', 'fast', true),
    ('clz8k3m1x0001abc123def456', 'Smarty', 'Smarty', 'Advanced reasoning and complex problem solving. Higher quality responses with better context understanding.', 'openrouter', 'google/gemini-2.5-pro', 'medium', 'medium', true),
    ('clz8k3m1x0002abc123def456', 'Big Brain', 'Big Brain', 'Most advanced model with superior reasoning capabilities. Best for complex tasks and creative content.', 'openai', 'openai/o3', 'high', 'slow', true)
ON CONFLICT ("name") DO NOTHING;

-- 8. Create updated_at trigger for ai_models
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updatedAt = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_ai_models_updated_at 
    BEFORE UPDATE ON "ai_models" 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- RLS (Row Level Security) Policies
-- =====================================================

-- 9. Enable RLS on ai_models table
ALTER TABLE "ai_models" ENABLE ROW LEVEL SECURITY;

-- 10. Policy: Everyone can read active AI models
CREATE POLICY "ai_models_select_policy" ON "ai_models"
    FOR SELECT USING ("isActive" = true);

-- 11. Policy: Only admins can insert AI models
CREATE POLICY "ai_models_insert_policy" ON "ai_models"
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM "users" 
            WHERE "users"."id" = auth.uid()::text 
            AND "users"."isAdmin" = true
        )
    );

-- 12. Policy: Only admins can update AI models
CREATE POLICY "ai_models_update_policy" ON "ai_models"
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM "users" 
            WHERE "users"."id" = auth.uid()::text 
            AND "users"."isAdmin" = true
        )
    );

-- 13. Policy: Only admins can delete AI models
CREATE POLICY "ai_models_delete_policy" ON "ai_models"
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM "users" 
            WHERE "users"."id" = auth.uid()::text 
            AND "users"."isAdmin" = true
        )
    );

-- 14. Update existing users policy to include modelId field
DROP POLICY IF EXISTS "users_update_own_policy" ON "users";
CREATE POLICY "users_update_own_policy" ON "users"
    FOR UPDATE USING ("id" = auth.uid()::text)
    WITH CHECK ("id" = auth.uid()::text);

-- =====================================================
-- Verification Queries (Optional - for testing)
-- =====================================================

-- Check if tables exist and have correct structure
-- SELECT table_name, column_name, data_type, is_nullable
-- FROM information_schema.columns 
-- WHERE table_name IN ('users', 'ai_models') 
-- ORDER BY table_name, ordinal_position;

-- Check if AI models were inserted
-- SELECT * FROM "ai_models" ORDER BY "name";

-- Check RLS policies
-- SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual
-- FROM pg_policies
-- WHERE tablename IN ('users', 'ai_models', 'personality_profiles', 'plan_features', 'subscription_plans');

-- =====================================================
-- Additional RLS Policies for Other Tables
-- =====================================================

-- 15. Enable RLS on personality_profiles table
ALTER TABLE "personality_profiles" ENABLE ROW LEVEL SECURITY;

-- 16. Policy: Everyone can read active personality profiles
CREATE POLICY "personality_profiles_select_policy" ON "personality_profiles"
    FOR SELECT USING ("isActive" = true);

-- 17. Policy: Only admins can insert personality profiles
CREATE POLICY "personality_profiles_insert_policy" ON "personality_profiles"
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM "users"
            WHERE "users"."id" = auth.uid()::text
            AND "users"."isAdmin" = true
        )
    );

-- 18. Policy: Only admins can update personality profiles
CREATE POLICY "personality_profiles_update_policy" ON "personality_profiles"
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM "users"
            WHERE "users"."id" = auth.uid()::text
            AND "users"."isAdmin" = true
        )
    );

-- 19. Policy: Only admins can delete personality profiles
CREATE POLICY "personality_profiles_delete_policy" ON "personality_profiles"
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM "users"
            WHERE "users"."id" = auth.uid()::text
            AND "users"."isAdmin" = true
        )
    );

-- 20. Enable RLS on subscription_plans table
ALTER TABLE "subscription_plans" ENABLE ROW LEVEL SECURITY;

-- 21. Policy: Everyone can read active subscription plans
CREATE POLICY "subscription_plans_select_policy" ON "subscription_plans"
    FOR SELECT USING ("isActive" = true);

-- 22. Policy: Only admins can insert subscription plans
CREATE POLICY "subscription_plans_insert_policy" ON "subscription_plans"
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM "users"
            WHERE "users"."id" = auth.uid()::text
            AND "users"."isAdmin" = true
        )
    );

-- 23. Policy: Only admins can update subscription plans
CREATE POLICY "subscription_plans_update_policy" ON "subscription_plans"
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM "users"
            WHERE "users"."id" = auth.uid()::text
            AND "users"."isAdmin" = true
        )
    );

-- 24. Policy: Only admins can delete subscription plans
CREATE POLICY "subscription_plans_delete_policy" ON "subscription_plans"
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM "users"
            WHERE "users"."id" = auth.uid()::text
            AND "users"."isAdmin" = true
        )
    );

-- 25. Enable RLS on plan_features table
ALTER TABLE "plan_features" ENABLE ROW LEVEL SECURITY;

-- 26. Policy: Everyone can read plan features (they're tied to subscription plans)
CREATE POLICY "plan_features_select_policy" ON "plan_features"
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM "subscription_plans"
            WHERE "subscription_plans"."id" = "plan_features"."planId"
            AND "subscription_plans"."isActive" = true
        )
    );

-- 27. Policy: Only admins can insert plan features
CREATE POLICY "plan_features_insert_policy" ON "plan_features"
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM "users"
            WHERE "users"."id" = auth.uid()::text
            AND "users"."isAdmin" = true
        )
    );

-- 28. Policy: Only admins can update plan features
CREATE POLICY "plan_features_update_policy" ON "plan_features"
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM "users"
            WHERE "users"."id" = auth.uid()::text
            AND "users"."isAdmin" = true
        )
    );

-- 29. Policy: Only admins can delete plan features
CREATE POLICY "plan_features_delete_policy" ON "plan_features"
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM "users"
            WHERE "users"."id" = auth.uid()::text
            AND "users"."isAdmin" = true
        )
    );

-- 30. Clean up duplicate users policies (remove old ones)
DROP POLICY IF EXISTS "Users can view own profile" ON "users";
DROP POLICY IF EXISTS "Users can update own profile" ON "users";

-- 31. Policy: Users can read their own profile
CREATE POLICY "users_select_own_policy" ON "users"
    FOR SELECT USING ("id" = auth.uid()::text);

-- 32. Policy: Users can insert their own profile (for auto-creation)
CREATE POLICY "users_insert_own_policy" ON "users"
    FOR INSERT WITH CHECK ("id" = auth.uid()::text);

-- 33. Policy: Admins can read all users
CREATE POLICY "users_select_admin_policy" ON "users"
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM "users" AS admin_user
            WHERE admin_user."id" = auth.uid()::text
            AND admin_user."isAdmin" = true
        )
    );

-- 34. Policy: Admins can update any user
CREATE POLICY "users_update_admin_policy" ON "users"
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM "users" AS admin_user
            WHERE admin_user."id" = auth.uid()::text
            AND admin_user."isAdmin" = true
        )
    );

-- 35. Policy: Admins can delete users
CREATE POLICY "users_delete_admin_policy" ON "users"
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM "users" AS admin_user
            WHERE admin_user."id" = auth.uid()::text
            AND admin_user."isAdmin" = true
        )
    );