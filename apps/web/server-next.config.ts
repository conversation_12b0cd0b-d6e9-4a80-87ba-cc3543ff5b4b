import { withSentryConfig } from "@sentry/nextjs";
import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  experimental: {
    clientTraceMetadata: ["test-header"],
  },
  // Disable static optimization globally to fix Clerk auth issues
  output: 'standalone',
  // Disable static generation completely
  trailingSlash: false,
  // Skip build static optimization
  skipTrailingSlashRedirect: true,
  // Disable static page generation
  generateBuildId: async () => {
    return 'build-' + Date.now()
  },
  // Force dynamic rendering for all pages to avoid static generation issues
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'no-cache, no-store, must-revalidate',
          },
        ],
      },
      {
        source: '/trpc/(.*)',
        headers: [
          {
            key: 'Access-Control-Allow-Origin',
            value: 'https://buddychip-web.vercel.app',
          },
          {
            key: 'Access-Control-Allow-Methods',
            value: 'GET, POST, PUT, DELETE, OPTIONS',
          },
          {
            key: 'Access-Control-Allow-Headers',
            value: 'Content-Type, Authorization, x-trpc-source',
          },
          {
            key: 'Access-Control-Allow-Credentials',
            value: 'true',
          },
        ],
      },
    ];
  },
};

// Proper Sentry configuration for production
const finalConfig = process.env.NODE_ENV === "production" 
  ? withSentryConfig(nextConfig, {
      org: "francesco-oddo",
      project: "buddychip-server",
      silent: !process.env.CI,
      widenClientFileUpload: true,
      disableLogger: true,
      automaticVercelMonitors: true,
    })
  : nextConfig;

export default finalConfig;
