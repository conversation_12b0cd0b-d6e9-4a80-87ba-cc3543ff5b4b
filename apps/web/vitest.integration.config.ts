/// <reference types="vitest" />
import { defineConfig } from 'vitest/config'
import { resolve } from 'path'

export default defineConfig({
  test: {
    // Test environment for integration tests
    environment: 'node',
    
    // Global setup for database and external services
    globalSetup: ['../../test/setup/integration-global-setup.ts'],
    setupFiles: ['../../test/setup/integration-setup.ts'],

    // Integration test patterns - focus on API endpoints and service interactions
    include: [
      '../../test/integration/**/*.{test,spec}.{ts,tsx}'
    ],
    exclude: [
      'node_modules',
      'dist',
      '.next',
      'coverage',
      'src/test/unit/**',
      'src/test/e2e/**',
      'src/test/fixtures/**',
      'src/test/mocks/**'
    ],
    
    // Longer timeouts for integration tests (database operations, API calls)
    testTimeout: 30000, // 30 seconds
    hookTimeout: 15000, // 15 seconds
    
    // Coverage for integration tests
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json'],
      reportsDirectory: './coverage/integration',
      include: [
        'src/routers/**/*.{ts,tsx}',
        'src/app/api/**/*.{ts,tsx}',
        'src/lib/db-utils.ts',
        'src/lib/context.ts'
      ],
      exclude: [
        '**/*.d.ts',
        '**/*.config.*',
        'src/test/**'
      ],
      thresholds: {
        global: {
          branches: 90,
          functions: 90,
          lines: 90,
          statements: 90
        }
      }
    },
    
    // Sequential execution for integration tests (database consistency)
    pool: 'threads',
    poolOptions: {
      threads: {
        singleThread: true, // Sequential for database tests
        maxThreads: 1,
        minThreads: 1
      }
    },
    
    // Test isolation with database cleanup
    isolate: true,
    
    // Retry for flaky external API calls
    retry: 1,
    
    // Reporter configuration
    reporters: ['verbose', 'json'],
    outputFile: {
      json: './test-results/integration-results.json'
    },
    
    // No watch mode for integration tests
    watch: false,
    
    // Full environment variables for integration tests
    env: {
      NODE_ENV: 'test',
      DATABASE_URL: 'postgresql://user:password@localhost:5432/buddychip_test',
      DIRECT_URL: 'postgresql://user:password@localhost:5432/buddychip_test',
      CLERK_SECRET_KEY: 'sk_test_mock_key_for_testing',
      CLERK_WEBHOOK_SIGNING_SECRET: 'whsec_mock_webhook_secret',
      OPENROUTER_API_KEY: 'mock_openrouter_key',
      OR_SITE_URL: 'http://localhost:3000',
      OR_APP_NAME: 'BuddyChip-Test',
      TWITTER_API_KEY: 'mock_twitter_key',
      OPENAI_API_KEY: 'mock_openai_key',
      XAI_API_KEY: 'mock_xai_key',
      PERPLEXITY_API_KEY: 'mock_perplexity_key',
      EXA_API_KEY: 'mock_exa_key',
      UPLOADTHING_TOKEN: 'mock_uploadthing_token',
      KV_URL: 'mock_kv_url',
      KV_TOKEN: 'mock_kv_token',
      SYNC_API_KEY: 'test_sync_api_key',
      ENABLE_PRISMA_QUERY_LOGS: 'false',
      ENABLE_CONTEXT_LOGS: 'true',
      ENABLE_TRPC_REQUEST_LOGS: 'true',
      VERBOSE_LOGGING: 'true'
    },
    
    // Globals for integration tests
    globals: true
  },
  
  // Path resolution
  resolve: {
    alias: {
      '@': resolve(__dirname, './src'),
      '~': resolve(__dirname, './'),
      '@/test': resolve(__dirname, './src/test')
    }
  },
  
  // Define configuration
  define: {
    'process.env.NODE_ENV': '"test"'
  }
})
