/// <reference types="vitest" />
import { defineConfig } from 'vitest/config'
import { resolve } from 'path'

export default defineConfig({
  test: {
    // Test environment
    environment: 'node',
    
    // No global setup - just basic tests
    setupFiles: [],
    
    // Test patterns
    include: [
      'src/basic.test.ts'
    ],
    exclude: [
      'node_modules',
      'dist',
      '.next',
      'coverage',
      'src/test/',
      'src/lib/**/*.test.ts',
      'src/routers/**/*.test.ts'
    ],
    
    // Test timeout
    testTimeout: 5000,
    hookTimeout: 5000,
    
    // Parallel execution
    pool: 'threads',
    poolOptions: {
      threads: {
        singleThread: true,
        maxThreads: 1,
        minThreads: 1
      }
    },
    
    // Test isolation
    isolate: true,
    
    // Reporter configuration
    reporters: ['verbose'],
    
    // Watch mode
    watch: false,
    
    // Environment variables for testing
    env: {
      NODE_ENV: 'test'
    }
  },
  
  // Path resolution
  resolve: {
    alias: {
      '@': resolve(__dirname, './src'),
      '~': resolve(__dirname, './'),
    }
  }
})