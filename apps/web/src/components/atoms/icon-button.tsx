'use client'
import React from 'react'
import type { IconType } from 'react-icons';

interface IconButtonProps {
  icon?: IconType | null;
  onClick?: () => void;
  className?: string;
  variant?: 'primary' | 'secondary' | 'tertiary';
  iconClassName?: string;
}

const IconButton = ({ icon=null, onClick, className, variant = 'primary', iconClassName }: IconButtonProps) => {
    const variantClasses = {
        primary: 'bg-app-secondary text-app-headline',
        secondary: 'bg-app-main text-app-secondary',
        tertiary: 'bg-app-stroke text-app-secondary'
    }
  const Icon = icon;

  return (
    <div className={`w-10 h-10 rounded-full flex items-center justify-center ${variantClasses[variant]} ${className} transition-all duration-300 cursor-pointer`} onClick={onClick}>
      {Icon && <Icon className={`w-[28px] h-[28px] ${iconClassName}`} />}
    </div>
  )
}

export default IconButton
