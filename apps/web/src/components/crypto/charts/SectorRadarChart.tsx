"use client"

interface Project {
  name: string
  mindshare?: number
  smartEngagementPoints?: number
}

interface SectorRadarChartProps {
  targetProject: Project
  competitors: Project[]
}

export default function SectorRadarChart({ targetProject, competitors }: SectorRadarChartProps) {
  // Simplified radar chart representation using CSS
  const metrics = [
    { 
      name: "Mindshare", 
      target: targetProject.mindshare || 0,
      competitors: competitors.map(c => c.mindshare || 0),
      max: 100
    },
    { 
      name: "Engagement", 
      target: targetProject.smartEngagementPoints || 0,
      competitors: competitors.map(c => c.smartEngagementPoints || 0),
      max: 1000
    },
  ]

  const normalizeValue = (value: number, max: number) => {
    return Math.min((value / max) * 100, 100)
  }

  const getCompetitorColor = (index: number) => {
    const colors = ["#ef4444", "#f97316", "#eab308"] // red, orange, yellow
    return colors[index] || "#6b7280"
  }

  return (
    <div className="space-y-4">
      <h5 className="text-sm font-medium text-app-headline">
        Competitive Positioning
      </h5>
      
      <div className="space-y-4">
        {metrics.map((metric) => (
          <div key={metric.name} className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm text-app-headline/70">{metric.name}</span>
              <span className="text-xs text-app-headline/50">
                Max: {metric.max === 100 ? "100" : "1K"}
              </span>
            </div>
            
            <div className="relative">
              {/* Background bar */}
              <div className="w-full h-8 bg-app-background rounded border border-app-stroke/30 relative overflow-hidden">
                {/* Target project bar */}
                <div 
                  className="absolute top-0 left-0 h-full bg-app-main/80 rounded transition-all duration-500"
                  style={{ width: `${normalizeValue(metric.target, metric.max)}%` }}
                />
                
                {/* Competitor markers */}
                {metric.competitors.map((value, index) => {
                  const position = normalizeValue(value, metric.max)
                  return (
                    <div
                      key={index}
                      className="absolute top-0 h-full w-1 opacity-80"
                      style={{ 
                        left: `${position}%`,
                        backgroundColor: getCompetitorColor(index)
                      }}
                    />
                  )
                })}
              </div>
              
              {/* Value labels */}
              <div className="flex items-center justify-between mt-1">
                <span className="text-xs font-medium text-app-main">
                  {targetProject.name}: {metric.target}
                </span>
                <div className="flex items-center gap-2">
                  {competitors.slice(0, 3).map((competitor, index) => (
                    <span 
                      key={index}
                      className="text-xs"
                      style={{ color: getCompetitorColor(index) }}
                    >
                      {competitor.name}: {metric.competitors[index]}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
      
      {/* Legend */}
      <div className="flex flex-wrap items-center gap-4 text-xs pt-2 border-t border-app-stroke/30">
        <div className="flex items-center gap-1">
          <div className="w-3 h-3 bg-app-main/80 rounded"></div>
          <span className="text-app-headline/70">{targetProject.name}</span>
        </div>
        
        {competitors.slice(0, 3).map((competitor, index) => (
          <div key={index} className="flex items-center gap-1">
            <div 
              className="w-3 h-1 rounded"
              style={{ backgroundColor: getCompetitorColor(index) }}
            ></div>
            <span className="text-app-headline/70 truncate max-w-[80px]">
              {competitor.name}
            </span>
          </div>
        ))}
      </div>
    </div>
  )
}