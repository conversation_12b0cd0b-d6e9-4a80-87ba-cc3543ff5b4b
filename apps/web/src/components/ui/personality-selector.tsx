'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from './button'
import { Textarea } from './textarea'
import { Switch } from './switch'
import { X, User, Bot, Briefcase, TrendingUp, GraduationCap, Smile, Zap, MessageCircle, Users } from 'lucide-react'
import { trpc } from '@/utils/trpc'
import { toast } from 'sonner'

interface PersonalitySelectorProps {
  isOpen: boolean
  onClose: () => void
}

interface PersonalityProfile {
  id: string
  name: string
  description: string
}

// Icon mapping for personality types
const PERSONALITY_ICONS: Record<string, any> = {
  'Tech Bro': Briefcase,
  'Crypto Degen': TrendingUp,
  'Academic': GraduationCap,
  'Comedian': Smile,
  'Motivational Coach': Zap,
}

export default function PersonalitySelector({ isOpen, onClose }: PersonalitySelectorProps) {
  const [selectedPersonality, setSelectedPersonality] = useState<string | null>(null)
  const [customPrompt, setCustomPrompt] = useState('')
  const [useFirst<PERSON><PERSON>, setUseFirst<PERSON>erson] = useState(true)
  
  // Get current user personality settings
  const { data: userSettings, refetch } = trpc.user.getPersonality.useQuery(undefined, {
    enabled: isOpen
  })
  
  // Get available personality profiles
  const { data: personalities } = trpc.user.getPersonalities.useQuery()
  
  // Update personality mutation
  const updatePersonalityMutation = trpc.user.updatePersonality.useMutation()

  // Load current settings when dialog opens
  useEffect(() => {
    if (isOpen && userSettings) {
      setSelectedPersonality(userSettings.personalityId)
      setCustomPrompt(userSettings.customSystemPrompt || '')
      setUseFirstPerson(userSettings.useFirstPerson ?? true)
    }
  }, [isOpen, userSettings])

  // Handle ESC key to close dialog
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose()
      }
    }

    if (isOpen) {
      document.addEventListener('keydown', handleEscape)
      document.body.style.overflow = 'hidden'
    }

    return () => {
      document.removeEventListener('keydown', handleEscape)
      document.body.style.overflow = 'unset'
    }
  }, [isOpen, onClose])

  const handleSave = async () => {
    try {
      await updatePersonalityMutation.mutateAsync({
        personalityId: selectedPersonality,
        customSystemPrompt: customPrompt.trim() || undefined,
        useFirstPerson: useFirstPerson
      })
      
      await refetch()
      toast.success('Personality settings saved!')
      onClose()
    } catch (error) {
      console.error('Error saving personality:', error)
      toast.error('Failed to save personality settings')
    }
  }

  const handleCancel = () => {
    // Reset to current saved values
    if (userSettings) {
      setSelectedPersonality(userSettings.personalityId)
      setCustomPrompt(userSettings.customSystemPrompt || '')
      setUseFirstPerson(userSettings.useFirstPerson ?? true)
    }
    onClose()
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black/50 backdrop-blur-sm"
        onClick={handleCancel}
      />
      
      {/* Dialog */}
      <div className="relative bg-app-card border border-app-stroke rounded-lg shadow-xl max-w-4xl w-full mx-4 p-6 max-h-[90vh] overflow-y-auto">
        {/* Close button */}
        <button
          onClick={handleCancel}
          className="absolute top-4 right-4 text-app-headline hover:text-app-main transition-colors"
        >
          <X className="w-5 h-5" />
        </button>

        {/* Header */}
        <div className="pr-8 mb-6">
          <h2 className="text-xl font-semibold text-app-headline mb-2 flex items-center">
            <User className="w-5 h-5 mr-2" />
            AI Personality Settings
          </h2>
          <p className="text-app-headline opacity-70 text-sm">
            Choose how your AI responds to tweets. Each personality affects the tone and style of generated replies.
          </p>
        </div>

        {/* Personality Options */}
        <div className="mb-6">
          <h3 className="text-lg font-medium text-app-headline mb-4">Select a Personality</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {personalities?.map((personality: PersonalityProfile) => {
              const IconComponent = PERSONALITY_ICONS[personality.name] || User
              const isSelected = selectedPersonality === personality.id
              
              return (
                <div
                  key={personality.id}
                  onClick={() => setSelectedPersonality(personality.id)}
                  className={`
                    p-4 border rounded-lg cursor-pointer transition-all
                    ${isSelected 
                      ? 'border-app-main bg-app-main/10 shadow-md' 
                      : 'border-app-stroke hover:border-app-main/50 hover:bg-app-background/50'
                    }
                  `}
                >
                  <div className="flex items-start space-x-3">
                    <IconComponent className={`w-6 h-6 mt-1 ${isSelected ? 'text-app-main' : 'text-app-headline opacity-60'}`} />
                    <div className="flex-1">
                      <h4 className={`font-medium ${isSelected ? 'text-app-main' : 'text-app-headline'}`}>
                        {personality.name}
                      </h4>
                      <p className="text-sm text-app-headline opacity-70 mt-1">
                        {personality.description}
                      </p>
                    </div>
                  </div>
                </div>
              )
            })}
            
            {/* None option */}
            <div
              onClick={() => setSelectedPersonality(null)}
              className={`
                p-4 border rounded-lg cursor-pointer transition-all
                ${selectedPersonality === null 
                  ? 'border-app-main bg-app-main/10 shadow-md' 
                  : 'border-app-stroke hover:border-app-main/50 hover:bg-app-background/50'
                }
              `}
            >
              <div className="flex items-start space-x-3">
                <Bot className={`w-6 h-6 mt-1 ${selectedPersonality === null ? 'text-app-main' : 'text-app-headline opacity-60'}`} />
                <div className="flex-1">
                  <h4 className={`font-medium ${selectedPersonality === null ? 'text-app-main' : 'text-app-headline'}`}>
                    Default (Benji)
                  </h4>
                  <p className="text-sm text-app-headline opacity-70 mt-1">
                    Professional, helpful AI assistant
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Response Mode Selection */}
        <div className="mb-6">
          <h3 className="text-lg font-medium text-app-headline mb-4 flex items-center">
            <MessageCircle className="w-5 h-5 mr-2" />
            Response Mode
          </h3>
          <div className="bg-app-background/50 border border-app-stroke/30 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <div className="flex items-center space-x-3 mb-2">
                  {useFirstPerson ? (
                    <User className="w-5 h-5 text-app-main" />
                  ) : (
                    <Users className="w-5 h-5 text-app-headline opacity-60" />
                  )}
                  <div>
                    <h4 className="font-medium text-app-headline">
                      {useFirstPerson ? 'Account Owner Mode' : 'External User Mode'}
                    </h4>
                    <p className="text-sm text-app-headline opacity-70">
                      {useFirstPerson 
                        ? 'AI responds as the account owner: "I love this idea!"' 
                        : 'AI responds as an external user: "You should consider this!"'
                      }
                    </p>
                  </div>
                </div>
              </div>
              <Switch
                checked={useFirstPerson}
                onCheckedChange={setUseFirstPerson}
                className="data-[state=checked]:bg-app-main"
              />
            </div>
            <div className="mt-3 pt-3 border-t border-app-stroke/20">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-xs">
                <div className={`p-2 rounded ${useFirstPerson ? 'bg-app-main/10 border border-app-main/30' : 'bg-app-background border border-app-stroke/30'}`}>
                  <div className="font-medium text-app-headline mb-1">{useFirstPerson ? '✓' : '○'} Account Owner Mode</div>
                  <div className="text-app-headline opacity-70">Responds as "I", "my", "me" - as if you own the account</div>
                </div>
                <div className={`p-2 rounded ${!useFirstPerson ? 'bg-app-main/10 border border-app-main/30' : 'bg-app-background border border-app-stroke/30'}`}>
                  <div className="font-medium text-app-headline mb-1">{!useFirstPerson ? '✓' : '○'} External User Mode</div>
                  <div className="text-app-headline opacity-70">Responds as "you", "they" - as an external follower</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Custom System Prompt */}
        <div className="mb-6">
          <h3 className="text-lg font-medium text-app-headline mb-2">Custom System Prompt</h3>
          <p className="text-sm text-app-headline opacity-70 mb-3">
            Add your own instructions that will be included with every response. This will be combined with the selected personality.
          </p>
          <Textarea
            value={customPrompt}
            onChange={(e) => setCustomPrompt(e.target.value)}
            placeholder="e.g., Always mention my latest product launch, or Always be extra enthusiastic about web development..."
            className="bg-app-background border-app-stroke text-app-headline min-h-[100px]"
            rows={4}
          />
          <p className="text-xs text-app-headline opacity-60 mt-2">
            Note: Response mode affects whether AI responds as the account owner or as an external user. Custom prompts are applied on top of this base behavior.
          </p>
        </div>

        {/* Actions */}
        <div className="flex justify-end space-x-3">
          <Button
            variant="outline"
            onClick={handleCancel}
            className="border-app-stroke text-app-headline hover:bg-app-background"
          >
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            disabled={updatePersonalityMutation.isPending}
            className="bg-app-main text-app-secondary hover:bg-app-highlight"
          >
            {updatePersonalityMutation.isPending ? 'Saving...' : 'Save Settings'}
          </Button>
        </div>
      </div>
    </div>
  )
}