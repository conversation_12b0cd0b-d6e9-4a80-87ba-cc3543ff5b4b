"use client";

import React from 'react'
import PrimaryButton from '../atoms/button'
import { MdArrowOutward } from 'react-icons/md'
import { useUser } from '@clerk/nextjs'
import { useRouter } from 'next/navigation'
import Link from 'next/link'

export function CTASection() {
  const { user, isLoaded } = useUser();
  const router = useRouter();

  const handleStartJourney = () => {
    if (user) {
      router.push('/dashboard');
    } else {
      router.push('/sign-in');
    }
  };

  console.log('🔍 CTASection: Rendering CTA section');

  return (
    <footer className="flex flex-col min-h-[100vh] items-center justify-start p-3 sm:p-4 md:px-[6rem] lg:px-[8rem] xl:px-[12rem] gap-4 sm:gap-6 md:gap-10 relative overflow-hidden bg-app-stroke">
      <div className="z-10 w-[1px] h-[100px] sm:h-[150px] md:h-[210px] bg-app-secondary"/>

      <div className='z-10 flex flex-col items-center justify-center max-w-full px-2 sm:px-4'>
        <div className="flex flex-col items-center justify-center gap-3 sm:gap-4 md:gap-6">
          <h1 className="text-app-secondary text-[clamp(1.5rem,6vw,4.875rem)] text-center leading-tight px-2 max-w-5xl">
            Join the AI-Powered Twitter Revolution
          </h1>
          <p className="text-app-secondary text-[clamp(0.875rem,3vw,1.5rem)] text-center px-2 sm:px-4 md:px-8 lg:px-16 max-w-4xl leading-relaxed">
            Built for Creators, Founders, and Growth Hackers. Harness the power of multi-model AI
            to dominate Twitter engagement and grow your audience effortlessly.
          </p>
        </div>

        <div className="flex flex-col sm:flex-row items-center justify-center gap-3 sm:gap-4 md:gap-6 mt-6 sm:mt-8 md:mt-[4rem] lg:mt-[6rem] w-full max-w-4xl px-2 sm:px-4">
          <PrimaryButton icon={MdArrowOutward} onClick={handleStartJourney} iconVariant="tertiary">
            Start your AI Journey!
          </PrimaryButton>
          <PrimaryButton icon={MdArrowOutward} onClick={() => {}} variant="primary" iconVariant="tertiary">
            View Features Demo
          </PrimaryButton>
        </div>
      </div>

      <video
        className="w-full h-full absolute bottom-0 sm:bottom-[-50px] md:bottom-[-100px] left-0 rotate-180 scale-x-[-1] object-cover [mask-image:linear-gradient(to_top,transparent_20%,black_60%)]"
        autoPlay
        muted
        loop
        playsInline
        controls={false}
        preload="metadata"
      >
        <source src="https://yo2pcnft8a.ufs.sh/f/nskFA2JaD20hZTYUIUQu4T7eFQBNcsKXxIC6tWAbMJhdfkp2" type="video/mp4" />
        Your browser does not support the video tag.
      </video>
  </footer>
  )
}
