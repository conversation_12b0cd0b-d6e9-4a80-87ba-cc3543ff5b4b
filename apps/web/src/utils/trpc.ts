import { QueryCache, QueryClient } from '@tanstack/react-query';
import { httpBatchLink } from '@trpc/client';
import { createTRPCReact } from '@trpc/react-query';
import { toast } from 'sonner';
import type { AppRouter } from '@/types/api';

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // Optimize caching for better performance
      staleTime: 1000 * 60 * 5, // 5 minutes - data stays fresh for 5 min
      gcTime: 1000 * 60 * 30, // 30 minutes - keep in cache for 30 min
      refetchOnWindowFocus: false, // Don't refetch when window regains focus
      refetchOnReconnect: 'always', // Do refetch when reconnecting
      retry: (failureCount, error: any) => {
        // Don't retry on 4xx errors (client errors)
        if (error?.data?.code >= 400 && error?.data?.code < 500) {
          return false;
        }
        // Retry up to 3 times for other errors
        return failureCount < 3;
      },
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000), // Exponential backoff
    },
    mutations: {
      retry: 1, // Only retry mutations once
      onError: (error: any) => {
        // Don't show toast for expected errors
        if (!error?.data?.code || error.data.code >= 500) {
          toast.error(error.message || 'An error occurred');
        }
      },
    },
  },
  queryCache: new QueryCache({
    onError: (error: any, query) => {
      // Only show toast for unexpected errors and if query isn't background refetch
      if (
        (!error?.data?.code || error.data.code >= 500) &&
        query.state.data !== undefined // Don't show error if we have cached data
      ) {
        toast.error(error.message, {
          action: {
            label: "retry",
            onClick: () => {
              queryClient.invalidateQueries({ queryKey: query.queryKey });
            },
          },
        });
      }
    },
  }),
});

export const trpc = createTRPCReact<AppRouter>();

export const trpcClient = trpc.createClient({
  links: [
    httpBatchLink({
      url: '/api/trpc',  // Local tRPC API route in same Next.js app
      maxURLLength: 2083, // Optimize URL length for batching
      // Headers for better caching and debugging
      headers() {
        return {
          'x-trpc-source': 'nextjs-react',
          // Add cache control hint for CDN/proxy caching
          'cache-control': 'public, max-age=60',
        };
      },
      // Add request/response interceptors for debugging in development
      fetch(url, options) {
        if (process.env.NODE_ENV === 'development') {
          const startTime = performance.now();
          console.log('🔄 tRPC request:', { url, method: options?.method });
          
          return fetch(url, options).then(response => {
            const duration = performance.now() - startTime;
            console.log(`✅ tRPC response (${duration.toFixed(2)}ms):`, {
              status: response.status,
              url: response.url,
            });
            return response;
          }).catch(error => {
            const duration = performance.now() - startTime;
            console.error(`❌ tRPC error (${duration.toFixed(2)}ms):`, error);
            throw error;
          });
        }
        return fetch(url, options);
      },
    }),
  ],
});

