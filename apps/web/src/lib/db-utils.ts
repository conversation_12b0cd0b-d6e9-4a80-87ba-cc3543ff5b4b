/**
 * Database Utility Functions for BuddyChip
 *
 * This module provides helper functions for common database operations,
 * rate limiting, and query optimizations using Prisma 6.9.0 best practices.
 */

import { PrismaClient, FeatureType } from "../../prisma/generated/index.js";

// Singleton Prisma client with optimizations
const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined;
};

// Debug environment variables
console.log('🔍 DB-Utils (Web): DATABASE_URL exists:', !!process.env.DATABASE_URL);
console.log('🔍 DB-Utils (Web): DATABASE_URL value:', process.env.DATABASE_URL?.substring(0, 50) + '...');
console.log('🔍 DB-Utils (Web): DIRECT_URL exists:', !!process.env.DIRECT_URL);
console.log('🔍 DB-Utils (Web): NODE_ENV:', process.env.NODE_ENV);

export const prisma = globalForPrisma.prisma ?? new PrismaClient({
  log: process.env.ENABLE_PRISMA_QUERY_LOGS === "true"
    ? ["query", "error", "warn"]
    : process.env.NODE_ENV === "development" ? ["warn", "error"] : ["error"],
  errorFormat: 'pretty',
  // Enhanced connection pool configuration for Supabase
  datasources: {
    db: {
      url: process.env.DATABASE_URL,
    },
  },
  // Connection pooling and retry configuration
  // https://www.prisma.io/docs/concepts/components/prisma-client/connection-management
});

if (process.env.NODE_ENV !== "production") globalForPrisma.prisma = prisma;

/**
 * Test database connection with retry logic
 */
export async function testDatabaseConnection(maxRetries: number = 3): Promise<boolean> {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`🔍 DB-Utils (Web): Testing database connection (attempt ${attempt}/${maxRetries})...`);
      await prisma.$queryRaw`SELECT 1 as test`;
      console.log('✅ DB-Utils (Web): Database connection successful');
      return true;
    } catch (error: any) {
      console.error(`❌ DB-Utils (Web): Database connection failed (attempt ${attempt}/${maxRetries}):`, {
        name: error?.name,
        message: error?.message,
        code: error?.code,
      });
      
      if (attempt === maxRetries) {
        console.error('💥 DB-Utils (Web): Database connection failed after all retries');
        return false;
      }
      
      // Wait before retry with exponential backoff
      const delay = Math.min(Math.pow(2, attempt) * 1000, 10000); // Cap at 10 seconds
      console.log(`⏳ DB-Utils (Web): Waiting ${delay}ms before retry...`);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  return false;
}

/**
 * Safely execute a database operation with connection retry
 */
export async function safeDbOperation<T>(
  operation: () => Promise<T>,
  operationName: string,
  maxRetries: number = 3
): Promise<T | null> {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`🔄 DB-Utils (Web): Attempting ${operationName} (attempt ${attempt}/${maxRetries})`);
      const result = await operation();
      console.log(`✅ DB-Utils (Web): ${operationName} successful`);
      return result;
    } catch (error) {
      console.error(`❌ DB-Utils (Web): ${operationName} failed (attempt ${attempt}/${maxRetries}):`, error);

      if (attempt === maxRetries) {
        console.error(`💥 DB-Utils (Web): ${operationName} failed after ${maxRetries} attempts`);
        return null;
      }

      // Wait before retry (exponential backoff)
      const delay = Math.pow(2, attempt) * 1000;
      console.log(`⏳ DB-Utils (Web): Waiting ${delay}ms before retry...`);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  return null;
}

/**
 * Get current billing period in YYYY-MM format
 */
export function getCurrentBillingPeriod(): string {
  const now = new Date();
  return `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;
}

/**
 * Check if user is within their plan limits for a specific feature
 */
export async function checkRateLimit(
  userId: string,
  feature: FeatureType,
  requestedAmount: number = 1
): Promise<{ allowed: boolean; currentUsage: number; limit: number; remaining: number }> {
  // Get user's plan and feature limit
  const user = await prisma.user.findUnique({
    where: { id: userId },
    include: {
      plan: {
        include: {
          features: {
            where: { feature },
          },
        },
      },
    },
  });

  if (!user) {
    throw new Error("User not found");
  }

  const planFeature = user.plan.features[0];
  if (!planFeature) {
    throw new Error(`Feature ${feature} not configured for plan ${user.plan.name}`);
  }

  // -1 means unlimited
  if (planFeature.limit === -1) {
    return {
      allowed: true,
      currentUsage: 0,
      limit: -1,
      remaining: -1,
    };
  }

  // Get current usage for this billing period
  const currentUsage = await prisma.usageLog.aggregate({
    where: {
      userId,
      feature,
      billingPeriod: getCurrentBillingPeriod(),
    },
    _sum: {
      amount: true,
    },
  });

  const usedAmount = currentUsage._sum.amount || 0;
  const remaining = Math.max(0, planFeature.limit - usedAmount);
  const allowed = usedAmount + requestedAmount <= planFeature.limit;

  return {
    allowed,
    currentUsage: usedAmount,
    limit: planFeature.limit,
    remaining,
  };
}

/**
 * Record usage for rate limiting
 */
export async function recordUsage(
  userId: string,
  feature: FeatureType,
  amount: number = 1,
  metadata?: Record<string, any>
): Promise<void> {
  await prisma.usageLog.create({
    data: {
      userId,
      feature,
      amount,
      billingPeriod: getCurrentBillingPeriod(),
      metadata: metadata || {},
    },
  });
}

/**
 * Get user with plan and usage statistics
 */
export async function getUserWithPlanAndUsage(userId: string) {
  const user = await prisma.user.findUnique({
    where: { id: userId },
    include: {
      plan: {
        include: {
          features: true,
        },
      },
    },
  });

  if (!user) {
    throw new Error("User not found");
  }

  // Get current usage for all features
  const currentPeriod = getCurrentBillingPeriod();
  const usageStats = await prisma.usageLog.groupBy({
    by: ['feature'],
    where: {
      userId,
      billingPeriod: currentPeriod,
    },
    _sum: {
      amount: true,
    },
  });

  // Combine plan features with current usage
  const featuresWithUsage = user.plan.features.map((feature: any) => {
    const usage = usageStats.find((u: any) => u.feature === feature.feature);
    const currentUsage = usage?._sum.amount || 0;
    const remaining = feature.limit === -1 ? -1 : Math.max(0, feature.limit - currentUsage);

    return {
      ...feature,
      currentUsage,
      remaining,
      percentUsed: feature.limit === -1 ? 0 : Math.round((currentUsage / feature.limit) * 100),
    };
  });

  return {
    ...user,
    plan: {
      ...user.plan,
      features: featuresWithUsage,
    },
  };
}

/**
 * Get mentions with optimized queries using relationJoins
 */
export async function getMentionsForAccount(
  accountId: string,
  limit: number = 50,
  cursor?: string
) {
  return prisma.mention.findMany({
    where: {
      accountId,
    },
    include: {
      account: {
        select: {
          twitterHandle: true,
          displayName: true,
        },
      },
      responses: {
        orderBy: {
          createdAt: 'desc',
        },
        take: 1, // Only get latest response
      },
      images: true,
    },
    orderBy: {
      mentionedAt: 'desc',
    },
    take: limit,
    ...(cursor && {
      cursor: { id: cursor },
      skip: 1,
    }),
  });
}

/**
 * Search mentions using full-text search
 */
export async function searchMentions(
  query: string,
  userId?: string,
  accountId?: string,
  limit: number = 20
) {
  return prisma.mention.findMany({
    where: {
      AND: [
        {
          content: {
            contains: query, // Simple text search for now
            mode: 'insensitive',
          },
        },
        ...(userId ? [{ user: { id: userId } }] : []),
        ...(accountId ? [{ accountId }] : []),
      ],
    },
    include: {
      account: {
        select: {
          twitterHandle: true,
          displayName: true,
        },
      },
      responses: {
        take: 1,
        orderBy: { createdAt: 'desc' },
      },
    },
    orderBy: {
      mentionedAt: 'desc',
    },
    take: limit,
  });
}

/**
 * Get AI response statistics for analytics
 */
export async function getAIResponseStats(userId?: string, days: number = 30) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);

  return prisma.aIResponse.aggregate({
    where: {
      ...(userId && { userId }),
      createdAt: {
        gte: startDate,
      },
    },
    _count: {
      id: true,
    },
    _avg: {
      confidence: true,
      rating: true,
      processingTime: true,
      tokensUsed: true,
    },
    _sum: {
      tokensUsed: true,
    },
  });
}

/**
 * Get trending keywords from recent mentions
 */
export async function getTrendingKeywords(days: number = 7, limit: number = 20) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);

  // Raw query for keyword aggregation
  const result = await prisma.$queryRaw<Array<{ keyword: string; count: bigint }>>`
    SELECT 
      unnest(keywords) as keyword,
      COUNT(*) as count
    FROM mentions 
    WHERE created_at >= ${startDate}
      AND keywords IS NOT NULL
      AND array_length(keywords, 1) > 0
    GROUP BY keyword
    ORDER BY count DESC
    LIMIT ${limit}
  `;

  return result.map((row: any) => ({
    keyword: row.keyword,
    count: Number(row.count),
  }));
}

/**
 * Batch update mention processing status
 */
export async function markMentionsAsProcessed(mentionIds: string[]) {
  return prisma.mention.updateMany({
    where: {
      id: {
        in: mentionIds,
      },
    },
    data: {
      processed: true,
    },
  });
}

/**
 * Clean up old usage logs (for maintenance)
 */
export async function cleanupOldUsageLogs(monthsToKeep: number = 12) {
  const cutoffDate = new Date();
  cutoffDate.setMonth(cutoffDate.getMonth() - monthsToKeep);

  const cutoffPeriod = `${cutoffDate.getFullYear()}-${String(cutoffDate.getMonth() + 1).padStart(2, '0')}`;

  return prisma.usageLog.deleteMany({
    where: {
      billingPeriod: {
        lt: cutoffPeriod,
      },
    },
  });
}

/**
 * Get subscription plan by name
 */
export async function getSubscriptionPlan(planName: string) {
  return prisma.subscriptionPlan.findUnique({
    where: { name: planName },
    include: { features: true },
  });
}

/**
 * Create or update user from Clerk webhook
 */
export async function upsertUserFromClerk(clerkUser: {
  id: string;
  email?: string;
  firstName?: string;
  lastName?: string;
  imageUrl?: string;
}) {
  const defaultPlan = await prisma.subscriptionPlan.findFirst({
    where: { name: 'reply-guy' },
  });

  if (!defaultPlan) {
    throw new Error("Default subscription plan not found");
  }

  return prisma.user.upsert({
    where: { id: clerkUser.id },
    update: {
      email: clerkUser.email,
      name: clerkUser.firstName && clerkUser.lastName 
        ? `${clerkUser.firstName} ${clerkUser.lastName}` 
        : clerkUser.firstName || clerkUser.lastName,
      avatar: clerkUser.imageUrl,
      lastActiveAt: new Date(),
    },
    create: {
      id: clerkUser.id,
      email: clerkUser.email,
      name: clerkUser.firstName && clerkUser.lastName 
        ? `${clerkUser.firstName} ${clerkUser.lastName}` 
        : clerkUser.firstName || clerkUser.lastName,
      avatar: clerkUser.imageUrl,
      planId: defaultPlan.id,
      lastActiveAt: new Date(),
    },
  });
}

// Export types for use in other modules
export type UserWithPlan = Awaited<ReturnType<typeof getUserWithPlanAndUsage>>;
export type MentionWithRelations = Awaited<ReturnType<typeof getMentionsForAccount>>[0];
export type RateLimitResult = Awaited<ReturnType<typeof checkRateLimit>>;