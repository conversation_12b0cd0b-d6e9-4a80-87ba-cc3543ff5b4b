import type { NextRequest } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { prisma } from "./db-utils";
import { getOrCreateUser } from "./user-service";

export async function createContext(req: NextRequest) {
  const enableContextLogs = process.env.ENABLE_CONTEXT_LOGS === "true";

  if (enableContextLogs) {
    console.log('🔍 Context: Creating context for request');
    console.log('🌐 Context: Request origin:', req.headers.get('origin'));
    // Note: Never log authorization headers in production for security
    console.log('🔑 Context: Authorization header present:', !!req.headers.get('authorization'));
  }

  try {
    const authResult = await auth();
  
  if (enableContextLogs) {
    console.log('👤 Context: Auth result:', { userId: authResult.userId });
  }

  // If user is authenticated, ensure they exist in our database using the existing service
  if (authResult.userId) {
    try {
      // Use the getOrCreateUser function which will create the user if they don't exist
      // We don't need Clerk user info since the function handles defaults
      await getOrCreateUser(authResult.userId, {
        email: undefined, // Will be set to default values by the service
        name: undefined,  // Will be set to default values by the service
        avatar: undefined // Will be set to default values by the service
      });
      
      if (enableContextLogs) {
        console.log('✅ Context: User ensured to exist in database');
      }
    } catch (error) {
      console.error('❌ Context: Error ensuring user exists:', error);
      // Continue anyway - the user service will handle missing users gracefully
    }
  }

    return {
      userId: authResult.userId,
      prisma,
      req,
    };
  } catch (error) {
    console.error('🔴 Context: Error creating context:', error);
    throw error;
  }
}



export type Context = Awaited<ReturnType<typeof createContext>>;
