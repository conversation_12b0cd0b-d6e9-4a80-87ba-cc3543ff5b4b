/**
 * Crypto Data Caching Service
 * 
 * Multi-layer caching system for crypto intelligence data:
 * 1. Database-level caching (PostgreSQL with TTL)
 * 2. Application-level caching (in-memory)
 * 3. Intelligent cache warming and invalidation
 */

import { PrismaClient } from '../../prisma/generated/index.js';

const prisma = new PrismaClient();

// Cache TTL constants (in milliseconds)
export const CACHE_TTL = {
  SECTORS: 60 * 60 * 1000,      // 1 hour (sectors rarely change)
  TRENDING: 15 * 60 * 1000,     // 15 minutes (trending data is dynamic)
  SMART_FOLLOWERS: 30 * 60 * 1000, // 30 minutes (moderate volatility)
} as const;

// Application-level cache (in-memory)
const appCache = new Map<string, { data: any; expires: number }>();

export interface CacheEntry<T = any> {
  data: T;
  expires: number;
  fromDatabase?: boolean;
  fromMemory?: boolean;
}

export class CryptoCacheService {
  
  /**
   * Get cached sectors with multi-layer fallback
   */
  async getCachedSectors(): Promise<CacheEntry | null> {
    const cacheKey = 'crypto_sectors';
    
    // 1. Check application cache first (fastest)
    const memoryEntry = appCache.get(cacheKey);
    if (memoryEntry && Date.now() < memoryEntry.expires) {
      console.log('🚀 Cache hit: Sectors from memory');
      return {
        data: memoryEntry.data,
        expires: memoryEntry.expires,
        fromMemory: true
      };
    }

    // 2. Check database cache (fast)
    try {
      const dbEntry = await prisma.$queryRaw<Array<{
        data: any;
        expires_at: Date;
      }>>`
        SELECT data, expires_at
        FROM crypto_sectors_cache 
        WHERE expires_at > NOW()
        ORDER BY created_at DESC
        LIMIT 1
      `;

      if (dbEntry.length > 0) {
        const entry = dbEntry[0];
        const expiresMs = entry.expires_at.getTime();
        
        // Update application cache
        appCache.set(cacheKey, {
          data: entry.data,
          expires: expiresMs
        });

        console.log('💾 Cache hit: Sectors from database');
        return {
          data: entry.data,
          expires: expiresMs,
          fromDatabase: true
        };
      }
    } catch (error) {
      console.error('🔴 Database cache error for sectors:', error);
    }

    return null;
  }

  /**
   * Cache sectors data with multi-layer storage
   */
  async cacheSectors(data: any): Promise<void> {
    const expiresAt = new Date(Date.now() + CACHE_TTL.SECTORS);
    const cacheKey = 'crypto_sectors';

    // 1. Store in application cache
    appCache.set(cacheKey, {
      data,
      expires: expiresAt.getTime()
    });

    // 2. Store in database cache
    try {
      await prisma.$executeRaw`
        INSERT INTO crypto_sectors_cache (data, expires_at)
        VALUES (${JSON.stringify(data)}::jsonb, ${expiresAt})
        ON CONFLICT DO NOTHING
      `;
      console.log('💾 Cached sectors in database and memory');
    } catch (error) {
      console.error('🔴 Failed to cache sectors in database:', error);
    }
  }

  /**
   * Get cached trending projects with sector/timeframe specificity
   */
  async getCachedTrending(sectorSlug?: string, timeframe: string = '_7Days'): Promise<CacheEntry | null> {
    const cacheKey = `crypto_trending_${sectorSlug || 'all'}_${timeframe}`;
    
    // 1. Check application cache first
    const memoryEntry = appCache.get(cacheKey);
    if (memoryEntry && Date.now() < memoryEntry.expires) {
      console.log(`🚀 Cache hit: Trending (${sectorSlug || 'all'}/${timeframe}) from memory`);
      return {
        data: memoryEntry.data,
        expires: memoryEntry.expires,
        fromMemory: true
      };
    }

    // 2. Check database cache
    try {
      const dbEntry = await prisma.$queryRaw<Array<{
        data: any;
        expires_at: Date;
      }>>`
        SELECT data, expires_at
        FROM crypto_trending_cache 
        WHERE (sector_slug = ${sectorSlug} OR (sector_slug IS NULL AND ${sectorSlug} IS NULL))
          AND timeframe = ${timeframe}
          AND expires_at > NOW()
        ORDER BY created_at DESC
        LIMIT 1
      `;

      if (dbEntry.length > 0) {
        const entry = dbEntry[0];
        const expiresMs = entry.expires_at.getTime();
        
        // Update application cache
        appCache.set(cacheKey, {
          data: entry.data,
          expires: expiresMs
        });

        console.log(`💾 Cache hit: Trending (${sectorSlug || 'all'}/${timeframe}) from database`);
        return {
          data: entry.data,
          expires: expiresMs,
          fromDatabase: true
        };
      }
    } catch (error) {
      console.error('🔴 Database cache error for trending:', error);
    }

    return null;
  }

  /**
   * Cache trending projects data
   */
  async cacheTrending(data: any, sectorSlug?: string, timeframe: string = '_7Days'): Promise<void> {
    const expiresAt = new Date(Date.now() + CACHE_TTL.TRENDING);
    const cacheKey = `crypto_trending_${sectorSlug || 'all'}_${timeframe}`;

    // 1. Store in application cache
    appCache.set(cacheKey, {
      data,
      expires: expiresAt.getTime()
    });

    // 2. Store in database cache
    try {
      await prisma.$executeRaw`
        INSERT INTO crypto_trending_cache (sector_slug, timeframe, data, expires_at)
        VALUES (${sectorSlug}, ${timeframe}, ${JSON.stringify(data)}::jsonb, ${expiresAt})
        ON CONFLICT (COALESCE(sector_slug, ''), timeframe) 
        WHERE expires_at > NOW()
        DO UPDATE SET 
          data = EXCLUDED.data,
          expires_at = EXCLUDED.expires_at,
          updated_at = NOW()
      `;
      console.log(`💾 Cached trending (${sectorSlug || 'all'}/${timeframe}) in database and memory`);
    } catch (error) {
      console.error('🔴 Failed to cache trending in database:', error);
    }
  }

  /**
   * Invalidate cache entries
   */
  async invalidateCache(type: 'sectors' | 'trending' | 'all', sectorSlug?: string, timeframe?: string): Promise<void> {
    console.log(`🗑️ Invalidating cache: ${type}`);

    if (type === 'sectors' || type === 'all') {
      // Clear application cache
      appCache.delete('crypto_sectors');
      
      // Clear database cache
      try {
        await prisma.$executeRaw`DELETE FROM crypto_sectors_cache WHERE expires_at > NOW()`;
      } catch (error) {
        console.error('🔴 Failed to invalidate sectors cache in database:', error);
      }
    }

    if (type === 'trending' || type === 'all') {
      // Clear application cache
      if (sectorSlug && timeframe) {
        appCache.delete(`crypto_trending_${sectorSlug}_${timeframe}`);
      } else {
        // Clear all trending cache entries
        for (const key of appCache.keys()) {
          if (key.startsWith('crypto_trending_')) {
            appCache.delete(key);
          }
        }
      }
      
      // Clear database cache
      try {
        if (sectorSlug && timeframe) {
          await prisma.$executeRaw`
            DELETE FROM crypto_trending_cache 
            WHERE (sector_slug = ${sectorSlug} OR (sector_slug IS NULL AND ${sectorSlug} IS NULL))
              AND timeframe = ${timeframe}
              AND expires_at > NOW()
          `;
        } else {
          await prisma.$executeRaw`DELETE FROM crypto_trending_cache WHERE expires_at > NOW()`;
        }
      } catch (error) {
        console.error('🔴 Failed to invalidate trending cache in database:', error);
      }
    }
  }

  /**
   * Cleanup expired cache entries
   */
  async cleanupExpiredCache(): Promise<{ sectorsDeleted: number; trendingDeleted: number }> {
    try {
      const [sectorsResult, trendingResult] = await Promise.all([
        prisma.$executeRaw`DELETE FROM crypto_sectors_cache WHERE expires_at <= NOW()`,
        prisma.$executeRaw`DELETE FROM crypto_trending_cache WHERE expires_at <= NOW()`
      ]);

      // Clean application cache
      const now = Date.now();
      for (const [key, entry] of appCache.entries()) {
        if (entry.expires <= now) {
          appCache.delete(key);
        }
      }

      console.log(`🧹 Cleaned up expired cache: ${sectorsResult} sectors, ${trendingResult} trending`);
      
      return {
        sectorsDeleted: Number(sectorsResult),
        trendingDeleted: Number(trendingResult)
      };
    } catch (error) {
      console.error('🔴 Failed to cleanup expired cache:', error);
      return { sectorsDeleted: 0, trendingDeleted: 0 };
    }
  }

  /**
   * Get cache statistics
   */
  async getCacheStats(): Promise<{
    memory: { size: number; keys: string[] };
    database: { sectors: number; trending: number };
  }> {
    try {
      const [sectorsCount, trendingCount] = await Promise.all([
        prisma.$queryRaw<Array<{ count: bigint }>>`SELECT COUNT(*) as count FROM crypto_sectors_cache WHERE expires_at > NOW()`,
        prisma.$queryRaw<Array<{ count: bigint }>>`SELECT COUNT(*) as count FROM crypto_trending_cache WHERE expires_at > NOW()`
      ]);

      return {
        memory: {
          size: appCache.size,
          keys: Array.from(appCache.keys())
        },
        database: {
          sectors: Number(sectorsCount[0]?.count || 0),
          trending: Number(trendingCount[0]?.count || 0)
        }
      };
    } catch (error) {
      console.error('🔴 Failed to get cache stats:', error);
      return {
        memory: { size: appCache.size, keys: Array.from(appCache.keys()) },
        database: { sectors: 0, trending: 0 }
      };
    }
  }

  /**
   * Warm cache by pre-loading commonly requested data
   */
  async warmCache(): Promise<void> {
    console.log('🔥 Warming crypto cache...');
    
    // This would typically pre-load:
    // 1. All sectors
    // 2. Trending projects for popular timeframes
    // 3. Other frequently accessed data
    
    // Implementation would call the actual Cookie.fun API
    // and populate cache proactively
  }
}

// Export singleton instance
export const cryptoCache = new CryptoCacheService();