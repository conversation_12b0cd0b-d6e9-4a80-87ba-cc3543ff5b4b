import {
  publicProcedure,
  createTRPCRouter,
} from "../lib/trpc";
import { benjiRouter } from "./benji";
import { userRouter } from "./user";
import { accountsRouter } from "./accounts";
import { mentionsRouter } from "./mentions";
import { twitterRouter } from "./twitter";
import { billingRouter } from "./billing";
import { cryptoRouter } from "./crypto";

export const appRouter = createTRPCRouter({
  healthCheck: publicProcedure.query(() => {
    return "OK";
  }),
  benji: benjiRouter,
  user: userRouter,
  accounts: accountsRouter,
  mentions: mentionsRouter,
  twitter: twitterRouter,
  billing: billingRouter,
  crypto: cryptoRouter,
});

export type AppRouter = typeof appRouter;
