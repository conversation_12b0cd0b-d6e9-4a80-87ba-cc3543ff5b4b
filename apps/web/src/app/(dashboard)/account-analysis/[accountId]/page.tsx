import { AccountTweetsFilter } from '@/components/account-tweets-filter';

interface PageProps {
  params: Promise<{
    accountId: string;
  }>;
}

export default async function AccountAnalysisPage({ params }: PageProps) {
  const { accountId } = await params;
  
  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-6">Account Analysis</h1>
      <AccountTweetsFilter accountId={accountId} />
    </div>
  );
}