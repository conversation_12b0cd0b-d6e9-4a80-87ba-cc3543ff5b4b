'use client';

import { UserTweets } from '@/components/user-tweets';

export default function TwitterAnalysisPage() {
  return (
    <div className="container mx-auto py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold">Twitter User Analysis</h1>
        <p className="text-muted-foreground mt-2">
          Analyze recent tweets from any Twitter user to understand their content patterns,
          engagement metrics, and posting behavior.
        </p>
      </div>
      
      <UserTweets />
      
      <div className="mt-12 space-y-4">
        <h2 className="text-xl font-semibold">How to use this tool:</h2>
        <ul className="list-disc list-inside space-y-2 text-muted-foreground">
          <li>Enter any Twitter username (with or without the @ symbol)</li>
          <li>Toggle whether to include replies and retweets in the results</li>
          <li>Click Search to fetch the user's recent tweets</li>
          <li>Use the Load More button to fetch additional tweets</li>
          <li>All fetched tweets are stored for future analysis</li>
        </ul>
        
        <div className="mt-8 p-4 bg-muted rounded-lg">
          <h3 className="font-semibold mb-2">Pro tip:</h3>
          <p className="text-sm text-muted-foreground">
            This tool uses the same Twitter API integration as our mention monitoring system.
            Fetched tweets are marked with the `isUserTweet` flag to distinguish them from mentions.
          </p>
        </div>
      </div>
    </div>
  );
}