import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON>eist, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import { <PERSON><PERSON>rovider } from '@clerk/nextjs';
import "../index.css";
import Providers from "@/components/providers";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "BuddyChip",
  description: "AI-powered social media monitoring and response tool for Twitter mentions",
  icons: {
    icon: [
      {
        url: '/Logo-L-Text.svg',
        type: 'image/svg+xml',
        sizes: 'any',
      },
      {
        url: '/Logo-L-Text.svg',
        type: 'image/svg+xml',
        sizes: '1024x1024',
      }
    ],
    shortcut: '/Logo-L-Text.svg',
    apple: {
      url: '/Logo-L-Text.svg',
      type: 'image/svg+xml',
      sizes: '512x512',
    },
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <ClerkProvider>
      <html lang="en" suppressHydrationWarning>
        <body
          className={`${geistSans.variable} ${geistMono.variable} antialiased`}
        >
          <Providers>
            {children}
          </Providers>
        </body>
      </html>
    </ClerkProvider>
  );
}