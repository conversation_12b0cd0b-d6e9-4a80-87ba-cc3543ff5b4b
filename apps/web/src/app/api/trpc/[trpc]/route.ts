import { fetchRe<PERSON><PERSON>and<PERSON> } from '@trpc/server/adapters/fetch';
import { appRouter } from '@/routers';
import { createContext } from '@/lib/context';
import { NextRequest } from 'next/server';

// Force dynamic rendering for auth compatibility
export const dynamic = 'force-dynamic';

export function OPTIONS() {
  return new Response(null, { status: 200 });
}

function handler(req: NextRequest) {
  if (process.env.ENABLE_TRPC_REQUEST_LOGS === "true") {
    console.log('🔄 tRPC request received:', {
      method: req.method,
      url: req.url,
      origin: req.headers.get('origin'),
      userAgent: req.headers.get('user-agent')?.substring(0, 50)
    });
  }

  return fetchRequestHandler({
    endpoint: '/api/trpc',
    req,
    router: appRouter,
    createContext: () => createContext(req),
    onError: ({ error, path, input }) => {
      console.error('🔴 tRPC Error:', {
        error: error.message,
        path,
        input,
        stack: error.stack
      });
    },
  });
}

export { handler as GET, handler as POST };