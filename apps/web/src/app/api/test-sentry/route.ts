import * as Sentry from "@sentry/nextjs";
import { NextResponse } from "next/server";

export async function GET() {
  try {
    // This will create a test error for <PERSON><PERSON>
    throw new Error("Test error for Sentry integration - this is intentional for testing");
  } catch (error) {
    // Capture the error with additional context
    Sentry.captureException(error, {
      tags: {
        component: "test-endpoint",
        test: "true"
      },
      extra: {
        timestamp: new Date().toISOString(),
        endpoint: "/api/test-sentry"
      }
    });

    return NextResponse.json(
      { 
        error: "Test error captured by Sen<PERSON>",
        message: "Check your Sentry dashboard for the error",
        timestamp: new Date().toISOString()
      }, 
      { status: 500 }
    );
  }
}