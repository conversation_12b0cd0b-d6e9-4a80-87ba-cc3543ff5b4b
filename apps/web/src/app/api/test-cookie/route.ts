import { NextResponse } from "next/server";
import { cookieClient } from "../../../lib/cookie-client";

/**
 * Test Cookie.fun API connectivity endpoint
 * GET /api/test-cookie
 */
export async function GET() {
  try {
    console.log('🔍 Testing Cookie.fun API connection...');
    
    // Test the API connection
    const connectionTest = await cookieClient.testConnection();
    
    if (!connectionTest.success) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Cookie.fun API connection failed',
          message: connectionTest.message,
          timestamp: new Date().toISOString()
        },
        { status: 503 }
      );
    }

    // Try to get sectors with fallback
    let sectorsResult = null;
    try {
      sectorsResult = await cookieClient.getSectors();
    } catch (error) {
      console.log('Sectors test failed:', error);
    }

    // Try to get trending projects
    let trendingResult = null;
    try {
      trendingResult = await cookieClient.getTrendingProjects(undefined, '_7Days');
    } catch (error) {
      console.log('Trending projects test failed:', error);
    }

    return NextResponse.json({
      success: true,
      message: 'Cookie.fun API tests completed',
      results: {
        connection: connectionTest,
        sectors: {
          success: !!sectorsResult,
          count: sectorsResult?.data?.length || 0,
          error: !sectorsResult ? 'Failed to fetch sectors' : null
        },
        trending: {
          success: !!trendingResult,
          count: trendingResult?.length || 0,
          error: !trendingResult ? 'Failed to fetch trending projects' : null
        }
      },
      timestamp: new Date().toISOString()
    });

  } catch (error: any) {
    console.error('❌ Cookie.fun API test failed:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: error.name || 'CookieAPIError',
        message: error.message || 'Unknown Cookie.fun API error',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}
