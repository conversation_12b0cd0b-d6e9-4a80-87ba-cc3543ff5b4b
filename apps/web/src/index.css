@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:where(.dark, .dark *));

@theme {
  --font-sans: "<PERSON>", "<PERSON>ei<PERSON>", ui-sans-serif, system-ui, sans-serif,
    "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}

html,
body {
  @apply bg-white dark:bg-gray-950;

  @media (prefers-color-scheme: dark) {
    color-scheme: dark;
  }
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  
  /* Custom app colors from PALETTE.md */
  --app-background: #d4d8f0;
  --app-headline: #232946;
  --app-sub-headline: #232946;
  --app-card: #fffffe;
  --app-card-heading: #232946;
  --app-card-paragraph: #232946;
  --app-stroke: #121629;
  --app-main: #b8c1ec;
  --app-highlight: #eebbc3;
  --app-secondary: #fffffe;
  --app-tertiary: #eebbc3;
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  
  /* Dark theme versions of custom app colors */
  --app-background: #121629;
  --app-headline: #fffffe;
  --app-sub-headline: #fffffe;
  --app-card: #232946;
  --app-card-heading: #fffffe;
  --app-card-paragraph: #fffffe;
  --app-stroke: #b8c1ec;
  --app-main: #6366f1;
  --app-highlight: #f87171;
  --app-secondary: #232946;
  --app-tertiary: #f87171;
  
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  
  /* Custom app colors for Tailwind */
  --color-app-background: var(--app-background);
  --color-app-headline: var(--app-headline);
  --color-app-sub-headline: var(--app-sub-headline);
  --color-app-card: var(--app-card);
  --color-app-card-heading: var(--app-card-heading);
  --color-app-card-paragraph: var(--app-card-paragraph);
  --color-app-stroke: var(--app-stroke);
  --color-app-main: var(--app-main);
  --color-app-highlight: var(--app-highlight);
  --color-app-secondary: var(--app-secondary);
  --color-app-tertiary: var(--app-tertiary);

  /* BuddyChipPro landing page colors for Tailwind */
  --color-buddychip-black: var(--buddychip-black);
  --color-buddychip-white: var(--buddychip-white);
  --color-buddychip-accent: var(--buddychip-accent);
  --color-buddychip-grey-stroke: var(--buddychip-grey-stroke);
  --color-buddychip-grey-text: var(--buddychip-grey-text);
  --color-buddychip-light-bg: var(--buddychip-light-bg);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

/* Custom keyframes for Tailwind CSS v4 */
@keyframes accordion-down {
  from { height: 0; }
  to { height: var(--radix-accordion-content-height); }
}

@keyframes accordion-up {
  from { height: var(--radix-accordion-content-height); }
  to { height: 0; }
}

@keyframes shard-loading-animation {
  0% {
    transform: translate(-50%, -50%) rotate(var(--angle)) translateY(calc(-1 * var(--radius))) scale(1);
    height: var(--base-height);
  }
  50% {
    transform: translate(-50%, -50%) rotate(var(--angle)) translateY(calc(-1.2 * var(--radius))) scale(1.1);
    height: calc(var(--base-height) * 1.3);
  }
  100% {
    transform: translate(-50%, -50%) rotate(var(--angle)) translateY(calc(-1 * var(--radius))) scale(1);
    height: var(--base-height);
  }
}

@keyframes smooth-scroll {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

.animate-smooth-scroll {
  animation: smooth-scroll 20s linear infinite;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Force landing page to always use light theme colors */
.landing-page {
  /* Override app colors to always use light theme values regardless of .dark class */
  --app-background: #d4d8f0 !important;
  --app-headline: #232946 !important;
  --app-sub-headline: #232946 !important;
  --app-card: #fffffe !important;
  --app-card-heading: #232946 !important;
  --app-card-paragraph: #232946 !important;
  --app-stroke: #121629 !important;
  --app-main: #b8c1ec !important;
  --app-highlight: #eebbc3 !important;
  --app-secondary: #fffffe !important;
  --app-tertiary: #eebbc3 !important;
}

/* Custom pricing section animations and effects */
@keyframes slide-in-from-bottom-4 {
  from {
    opacity: 0;
    transform: translateY(1rem);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-in-from-top-2 {
  from {
    opacity: 0;
    transform: translateY(-0.5rem);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-in {
  animation-fill-mode: forwards;
}

.slide-in-from-bottom-4 {
  animation: slide-in-from-bottom-4 0.5s ease-out;
}

.slide-in-from-top-2 {
  animation: slide-in-from-top-2 0.3s ease-out;
}

.delay-100 {
  animation-delay: 100ms;
}

.delay-200 {
  animation-delay: 200ms;
}

.hover\:scale-102:hover {
  transform: scale(1.02);
}

.active\:scale-98:active {
  transform: scale(0.98);
}
