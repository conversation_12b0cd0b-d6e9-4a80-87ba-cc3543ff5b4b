import * as Sentry from "@sentry/nextjs";

Sentry.init({
  dsn: process.env.SENTRY_DSN,

  // Set tracesSampleRate to 1.0 to capture 100%
  // of the transactions for performance monitoring.
  // We recommend adjusting this value in production
  tracesSampleRate: 1.0,

  // Capture Replay for 10% of all sessions,
  // plus for 100% of sessions with an error
  replaysSessionSampleRate: 0.1,
  replaysOnErrorSampleRate: 1.0,

  // Add better error filtering for server-side
  beforeSend(event) {
    // Filter out common non-critical errors
    if (event.exception) {
      const error = event.exception.values?.[0];
      if (error?.type === "AbortError" || 
          error?.value?.includes("Non-Error promise rejection captured")) {
        return null;
      }
    }
    return event;
  },

  // Note: if you want to override the automatic release value, do not set a
  // `release` value here - use the environment variable `SENTRY_RELEASE`, so
  // that it will also get attached to your source maps
});